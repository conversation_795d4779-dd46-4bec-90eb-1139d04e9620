{"name": "html2canvas-proxy", "version": "1.0.1", "description": "Cross-origin content proxy for html2canvas", "main": "server.js", "type": "commonjs", "scripts": {"test": "exit 0"}, "repository": {"type": "git", "url": "https://github.com/niklasvh/html2canvas-proxy-nodejs.git"}, "keywords": ["html2canvas", "proxy"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/niklasvh/html2canvas-proxy-nodejs/issues"}, "homepage": "https://github.com/niklasvh/html2canvas-proxy-nodejs", "dependencies": {"cheerio": "^1.0.0", "cors": "2.8.4", "html2canvas": "^1.4.1", "jsdom": "^26.1.0", "node-window-polyfill": "^1.0.4", "request": "2.87.0"}, "peerDependencies": {"express": "4.x"}}