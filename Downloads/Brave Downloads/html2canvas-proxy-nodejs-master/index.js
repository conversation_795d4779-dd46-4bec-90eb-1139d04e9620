import * as cheerio from 'cheerio';
import html2canvas from 'html2canvas';
import jsdom from "jsdom";
import nodeWindowPolyfill from "node-window-polyfill";
const $ = await cheerio.fromURL('http://localhost:3090/')


const { JSDOM } = jsdom;

nodeWindowPolyfill.register();


console.log("abcde", $)
global.document = new JSDOM($.html()).window.document;

// const $ = await cheerio.fromURL('http://localhost:3000/gist/aaf3c2d96b88b374b7867354d4e789e4')
// const app = new Hono()

// app.get('/', async (_c) => {
//   const response = await fetch('https://gist-hub.nuxt.dev/gist/aaf3c2d96b88b374b7867354d4e789e4')
//   // clone the response to return a response with modifiable headers
//   const newResponse = new Response(response.body, response)
//   const canvas2 = await html2canvas(newResponse, {
//     scale: 1,
//     useCORS: true,
//     allowTaint: true,
//     windowWidth: 1200,
//     windowHeight: 630
//   })
//   const blob: Blob = await new Promise(resolve => canvas2.toBlob(resolve, 'image/webp'))
//   console.log({ blob })
//   return newResponse
// })
// const response = await fetch('https://gist-hub.nuxt.dev/gist/aaf3c2d96b88b374b7867354d4e789e4')

// console.log({ response })

// clone the response to return a response with modifiable headers
const canvas2 = await html2canvas(document.body, {
  scale: 1,
  useCORS: false,
  allowTaint: true,
  windowWidth: 1200,
  windowHeight: 630
})
const blob = await new Promise(resolve => canvas2.toBlob(resolve, 'image/webp'))
// console.log({ blob })

// export default app
